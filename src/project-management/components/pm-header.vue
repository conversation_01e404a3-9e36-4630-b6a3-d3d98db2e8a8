<script setup>
import dayjs from 'dayjs';
import { storeToRefs } from 'pinia';
import { useModal } from 'vue-final-modal';
import { onBeforeRouteLeave, useRouter } from 'vue-router';
import HawkDeletePopup from '~/common/components/organisms/hawk-delete-popup.vue';
import HawkShare from '~/common/components/organisms/hawk-share.vue';
import { useCommonStore } from '~/common/stores/common.store';
import { getCookie } from '~/common/utils/common.utils';
import PmConfigureAutoSyncModal from '~/project-management/components/menus/pm-configure-auto-sync-modal.vue';
import PmExportModal from '~/project-management/components/menus/pm-export-modal.vue';
import PmImportModal from '~/project-management/components/menus/pm-import-modal.vue';
import PmShortcutsModal from '~/project-management/components/menus/pm-shortcuts-modal.vue';
import PmSyncHistoryModal from '~/project-management/components/menus/pm-sync-history-modal.vue';
import PmSyncNowModal from '~/project-management/components/menus/pm-sync-now-modal.vue';
import PmVersionsModal from '~/project-management/components/menus/pm-versions-modal.vue';
import PmDeleteTrackingsModal from '~/project-management/components/pm-delete-trackings-modal.vue';
import PmEditView from '~/project-management/components/pm-edit-view.vue';
import PmLeaveModal from '~/project-management/components/pm-leave-modal.vue';
import { useExportProgressReport } from '~/project-management/composables/pm-export-progress-report.composable';
import { useHelpers } from '~/project-management/composables/pm-helpers.composable';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const $t = inject('$t');
const $date = inject('$date');
const $toast = inject('$toast');
const router = useRouter();

const project_management_store = useProjectManagementStore();
const common_store = useCommonStore();

const {
  $g,
  flags,
  active_tab,
  active_view,
  is_pm_loading,
  view_dirtiness,
  active_schedule,
  schedule_dirtiness,
  active_schedule_data,
  is_schedule_editable,
} = storeToRefs(project_management_store);

const {
  update_view,
  delete_schedule,
  publish_schedule,
  set_view_dirtiness,
  unpublish_schedule,
  save_editable_schedule,
  update_active_schedule,
} = project_management_store;

const { openSetPlannedValuesModal } = useHelpers();

const {
  setExporting,
  is_report_exporting,
  exportScheduleReports,
  cancelExportingScheduleReports,
} = useExportProgressReport();

const state = reactive({
  is_saving: false,
  calendar_type: 'regular',
});

const leave_modal = useModal({
  component: PmLeaveModal,
  attrs: {
    save: onSave,
  },
});

const auto_sync_modal = useModal({
  component: PmConfigureAutoSyncModal,
  attrs: {
    onClose() {
      auto_sync_modal.close();
    },
  },
});

const sync_now_modal = useModal({
  component: PmSyncNowModal,
  attrs: {
    onClose() {
      sync_now_modal.close();
    },
  },
});

const sync_history_modal = useModal({
  component: PmSyncHistoryModal,
  attrs: {
    onClose() {
      sync_history_modal.close();
    },
  },
});

const versions_modal = useModal({
  component: PmVersionsModal,
  attrs: {
    onClose() {
      versions_modal.close();
    },
  },
});

const share_modal = useModal({
  component: HawkShare,
});

const import_modal = useModal({
  component: PmImportModal,
  attrs: {
    onClose() {
      import_modal.close();
    },
  },
});

const export_modal = useModal({
  component: PmExportModal,
  attrs: {
    onClose() {
      export_modal.close();
    },
  },
});

const delete_popup = useModal({
  component: HawkDeletePopup,
});

const pm_shortcuts_modal = useModal({
  component: PmShortcutsModal,
  attrs: {
    onClose() {
      pm_shortcuts_modal.close();
    },
  },
});

const new_view_modal = useModal({
  component: PmEditView,
  attrs: {
    onClose() {
      new_view_modal.close();
    },
    onSave() {
      set_view_dirtiness(false);
      new_view_modal.close();
    },
  },
});

const delete_trackings_modal = useModal({
  component: PmDeleteTrackingsModal,
  attrs: {
    onClose() {
      openShareModal();
      delete_trackings_modal.close();
    },
  },
});

const menu_items = computed(() => {
  const items = [
    ...(active_schedule.value?.actions?.sync_progress
      ? [[
          'sync_progress',
          IconHawkRefreshCcwFive,
          'Sync progress',
          null,
          [
            [
              'sync_now',
              IconHawkZap,
              'Sync Now',
              () => {
                sync_now_modal.open();
              },
            ],
            [
              'configure_auto_sync',
              IconHawkToolOne,
              'Configure auto sync',
              () => {
                auto_sync_modal.open();
              },
            ],
            [
              'history',
              IconHawkClockFastForward,
              'History',
              () => {
                sync_history_modal.open();
              },
            ],
          ].map((item) => {
            return {
              name: item[0],
              left_icon: item[1],
              label: $t(item[2]),
              on_click: item[3],
            };
          }),
        ]]
      : []),
    [
      'export',
      IconHawkDownloadOne,
      'Export',
      null,
      [
        [
          'export_schedule',
          IconHawkFileDownloadTwo,
          'Schedule',
          () => export_modal.open(),
        ],
        [
          'export_progress_history',
          IconHawkBarChartSquareDown,
          'Progress history',
          () => setExporting(true),
        ],
      ].map((item) => {
        return {
          name: item[0],
          left_icon: item[1],
          label: $t(item[2]),
          on_click: item[3],
        };
      }),
    ],
    ...(active_schedule.value?.actions?.modify
      ? [[
          'set_planned_values',
          IconHawkCalendarPercentage,
          'Set planned values',
          () => {
            openSetPlannedValuesModal(active_schedule_data.value.data[0].id);
          },
        ]]
      : []),
    ...(active_schedule.value?.actions?.modify
      ? [[
          'import',
          IconHawkFileUpload,
          'Import',
          () => import_modal.open(),
        ]]
      : []),
    ...(active_schedule.value?.actions?.modify_versions
      ? [[
          'versions',
          IconHawkUserOne,
          'Versions',
          () => versions_modal.open(),
          [],
        ]]
      : []),
    ...(active_schedule.value?.actions?.modify
      ? [[
          'share',
          IconHawkShareSeven,
          'Share',
          () => openShareModal(),
        ]]
      : []),
    ...(active_schedule.value?.actions?.publish_unpublish && active_schedule.value?.is_editable
      ? [[
          'unpublish',
          IconHawkRollback,
          'Unpublish',
          () => onSubmit('unpublish'),
        ]]
      : []),
    ...(active_schedule.value?.actions?.delete
      ? [[
          'delete',
          IconHawkTrashThree,
          'Delete',
          () => scheduleDeleteHandler(),
        ]]
      : []),
    [
      'keyboard_shortcuts',
      IconHawkKeyMac,
      'Keyboard Shortcuts',
      () => pm_shortcuts_modal.open(),
    ],
  ];

  let filtered_items;

  if (is_schedule_editable.value)
    filtered_items = items.filter(i => ['share', 'import', 'delete', 'keyboard_shortcuts'].includes(i[0]));

  else
    filtered_items = items.filter(i => !['keyboard_shortcuts', 'import'].includes(i[0]));

  if (active_schedule.value.is_manual)
    filtered_items = filtered_items.filter(i => !['versions'].includes(i[0]));

  return filtered_items.map((item) => {
    return {
      name: item[0],
      left_icon: item[1],
      label: $t(item[2]),
      on_click: item[3],
      submenu_items: item?.[4],
      disabled: !!item?.[5],
    };
  });
});

const users_and_teams = computed(() => {
  const users = [];
  const teams = [];
  const all_members = Object.entries(active_schedule.value?.members);
  all_members.forEach((element) => {
    const new_el = {
      uid: element[0],
      access: element[1],
    };
    const teams_uids = Object.keys(common_store.teams_map);
    if (teams_uids?.includes(new_el.uid))
      teams.push(new_el);
    else users.push(new_el);
  });

  return {
    users,
    teams,
  };
});

function scheduleDeleteHandler() {
  delete_popup.patchOptions(
    {
      attrs: {
        header: $t('Delete'),
        content: `${$t('Are you sure you want to delete this schedule')}?`,
        match_text: `${active_schedule.value.name || ''}`,
        match_text_input_placeholder: `${$t('Enter the name of the schedule to delete')}`,
        button_text: $t('Delete'),
        onClose() {
          delete_popup.close();
        },
        confirm: async () => {
          try {
            const active_schedule_uid = active_schedule.value.uid;
            await delete_schedule(active_schedule_uid);
            schedule_dirtiness.value = false;
            for (const key of Object.keys(common_store.assets_custom_fields_map)) {
              if (common_store.assets_custom_fields_map[key].properties.schedule_uid === active_schedule_uid) {
                delete common_store.assets_custom_fields_map[key].properties.schedule_uid;
                delete common_store.assets_custom_fields_map[key].properties.activity_uid;
              }
            }
            delete_popup.close();
            router.push({ name: 'project-management' });
          }
          catch (err) {
            logger.error(err);
            $toast({
              title: $t('Something went wrong'),
              text: $t('Please try again'),
              type: 'error',
            });
          }
        },
      },
    },
  );
  delete_popup.open();
}

async function onShare(data) {
  let public_access = false;
  if (data.global_access_level === 'read')
    public_access = true;

  const members = {
    add: [],
    update: [],
    delete: [],
  };

  const user_uids = data.users.map(user => user.uid);
  data.users.forEach((element) => {
    if (active_schedule.value.members[element.uid] === element.access)
      return;

    if (!active_schedule.value.members[element.uid]) {
      members.add.push({ user_uid: element.uid, permission: element.access });
      return;
    }

    if (active_schedule.value.members[element.uid] !== element.access)
      members.update.push({ user_uid: element.uid, permission: element.access });
  });

  const teams_uids = data.teams.map(team => team.uid);
  data.teams.forEach((element) => {
    if (active_schedule.value.members[element.uid] === element.access)
      return;

    if (!active_schedule.value.members[element.uid]) {
      members.add.push({ user_uid: element.uid, permission: element.access });
      return;
    }

    if (active_schedule.value.members[element.uid] !== element.access)
      members.update.push({ user_uid: element.uid, permission: element.access });
  });
  Object.keys(active_schedule.value.members).forEach((uid) => {
    if (!teams_uids.includes(uid) && !user_uids.includes(uid)) {
      members.delete.push(uid);
    }
  });

  const user_resource_mapping = active_schedule.value.resources.reduce((acc, resource) => {
    if (resource.external_id)
      acc[resource.external_id] = resource.uid;
    return acc;
  }, {});

  const deleted_member_resource_uids = members.delete.map((uid) => {
    return user_resource_mapping[uid];
  });

  const trackings_in_question = active_schedule.value.trackings.filter((tracking) => {
    return deleted_member_resource_uids.includes(tracking.resource);
  });

  if (members.delete.length
    && active_schedule_data.value?.data?.[0]?.actions?.clear_trackings
    && trackings_in_question.length
  ) {
    const members_with_trackings = members.delete.filter((member) => {
      return trackings_in_question.some((tracking) => {
        return tracking.resource === user_resource_mapping[member];
      });
    });
    const members_without_trackings = members.delete.filter((member) => {
      return !members_with_trackings.includes(member);
    });
    delete_trackings_modal.patchOptions({
      attrs: {
        memberUids: members_with_trackings,
        async onSave(delete_trackings_data) {
          members.delete = [];
          for (const key in delete_trackings_data) {
            members.delete.push({ user_uid: key, clear_tracking: delete_trackings_data[key] });
          }
          members_without_trackings.forEach((member) => {
            members.delete.push({ user_uid: member, clear_tracking: false });
          });
          const fetched_schedule = await update_active_schedule(
            active_schedule.value.uid,
            {
              members,
              public: public_access,
            },
            true,
          );
          active_schedule.value.members = fetched_schedule.data.members;
          active_schedule.value.resources = active_schedule.value.resources.filter((resource) => {
            if (resource.type === 'custom')
              return true;
            return !Object.keys(delete_trackings_data).includes(resource.external_id);
          });
          $g.value.render();
          delete_trackings_modal.close();
        },
      },
    });
    delete_trackings_modal.open();
  }
  else {
    if (members.delete.length) {
      members.delete = members.delete.map((uid) => {
        return { user_uid: uid, clear_tracking: false };
      });
    }
    const fetched_schedule = await update_active_schedule(
      active_schedule.value.uid,
      {
        members,
        public: public_access,
      },
      true,
    );
    active_schedule.value.members = fetched_schedule.data.members;
    $g.value.render();
  }
}

async function toggleDynamicness() {
  await update_active_schedule(
    active_schedule.value.uid,
    {
      is_dynamic_loading: !active_schedule.value.is_dynamic_loading,
    },
    true,
  );
}

function openShareModal() {
  if (!active_schedule.value.actions.modify)
    return;
  let global_access_level;
  if (active_schedule.value?.public)
    global_access_level = 'read';

  share_modal.patchOptions({
    attrs: {
      onClose() {
        share_modal.close();
      },
      is_modal: true,
      is_global_access_visible: true,
      owner_uid: active_schedule.value?.owner?.uid,
      teams: users_and_teams.value.teams,
      members: users_and_teams.value.users,
      global_access_level,
      global_access_levels: [
        { name: 'no_access', label: $t('No access') },
        { name: 'read', label: $t('Viewer'), description: $t('View schedule, activities and all information related to the schedule') },
      ],
      access_levels: [
        { name: 'read', label: $t('Viewer'), description: $t('View schedule, activities and all information related to the schedule') },
        { name: 'manage', label: $t('Manager'), description: $t('View entire schedule, manage resources, assignments, progress configurations, progress updates and integrations') },
        // $t('Can edit, publish, unpublish, modify activities and manage the schedule')
        { name: 'write', label: $t('Admin'), description: $t('Full access to the schedule') },
      ],
      get_share_data(data) {
        onShare(data);
      },
    },
  });
  share_modal.open();
}

async function onSubmit(type = 'publish') {
  if (!active_schedule.value.is_editable) {
    $toast({
      title: $t('This schedule cannot be edited.'),
      type: 'error',
      timeout: 4000,
    });
    return;
  }
  if (state.is_saving)
    return;
  try {
    is_pm_loading.value = true;
    state.is_saving = true;
    if (type === 'publish')
      await publish_schedule();
    else if (type === 'unpublish')
      await unpublish_schedule();
    router.go();
  }
  catch (error) {
    logger.error(error);
    $toast({
      title: error.data.title ?? $t('Something went wrong'),
      text: error.data.description ?? $t('Please try again'),
      type: 'error',
      timeout: 4000,
    });
  }
  finally {
    state.is_saving = false;
    is_pm_loading.value = false;
  }
}

async function onSave() {
  if (!active_schedule.value.is_editable) {
    $toast({
      title: $t('This schedule cannot be edited.'),
      type: 'error',
      timeout: 4000,
    });
    return;
  }
  if (state.is_saving)
    return;
  is_pm_loading.value = true;
  state.is_saving = true;
  const updated_at = dayjs().toISOString();
  await save_editable_schedule({
    relations: $g.value.getLinks(),
  }, $t);
  active_schedule.value.updated_at = updated_at;
  is_pm_loading.value = false;
  state.is_saving = false;
}

async function updateView() {
  if (active_view.value?.uid === '__default') {
    new_view_modal.patchOptions({ attrs: { mode: 'add' } });
    new_view_modal.open();
  }
  else {
    state.is_saving = true;
    await update_view(active_view.value);
    state.is_saving = false;

    $toast({
      text: 'Custom view has been saved successfully.',
      type: 'success', // one of 'success', 'warning', 'error', and 'info' (default 'info')
      title: 'Successfully saved view', // (optional)
      timeout: 2000, // timeout in ms (default 2 seconds)
      has_close_button: true,
    });

    set_view_dirtiness(false);
  }
}

function getLatestDate(date_one, date_two) {
  if (!date_one)
    return date_two;
  if (!date_two)
    return date_one;
  return dayjs(date_one).isAfter(dayjs(date_two)) ? date_one : date_two;
}

function backToScheduleList() {
  router.push({ name: 'project-management' });
}

function openLeaveModal(next) {
  leave_modal.patchOptions({
    attrs: {
      onClose() {
        window.history.forward();
        leave_modal.close();
      },
      onDiscard: async () => {
        leave_modal.close();
        next();
      },
    },
  });
  leave_modal.open();
}

onBeforeRouteLeave((to, from, next) => {
  if (is_schedule_editable.value && schedule_dirtiness.value)
    openLeaveModal(next);
  else next();
});
</script>

<template>
  <HawkExportToast
    v-if="is_report_exporting"
    :submit="exportScheduleReports"
    :progress_text="$t('Exporting progress report')"
    :completed_text="$t('Progress report exported successfully')"
    @cancel="cancelExportingScheduleReports"
    @close="() => setExporting(false)"
  />
  <div class="flex items-center justify-between px-4 pt-2.5 pb-1.5">
    <div class="flex items-center">
      <HawkButton
        icon
        type="text"
        @click="backToScheduleList"
      >
        <IconHawkChevronLeft class="w-5 h-5" />
      </HawkButton>
      <div class="flex flex-col ml-2">
        <div class="flex items-center font-semibold text-md">
          <span
            v-tippy="{
              content: active_schedule.name?.length > 50 ? active_schedule.name : '',
              placement: 'bottom',
            }"
            class="mr-2 max-w-[calc(100vw-600px)] truncate"
          >
            {{ active_schedule.name }}
          </span>
          <HawkButton v-if="getCookie('__PM_ENABLE_TOGGLE_BUTTON_FOR_DYNAMIC_LOADING')" size="xs" type="outlined" class="ml-4" @click="toggleDynamicness">
            Set as {{ active_schedule?.is_dynamic_loading ? 'not dynamic' : 'dynamic' }}
          </HawkButton>
        </div>
        <div class="flex items-center gap-2 text-xs">
          <div class="text-xs font-normal text-gray-900">
            <span class="text-gray-500">
              {{ $t('Last modified') }}:
            </span>
            <span class="text-gray-600">
              <template v-if="is_schedule_editable">
                {{ $date(getLatestDate(active_schedule.updated_at, active_schedule.created_at), 'DD MMMM YYYY, hh:mma') }}
              </template>
              <template v-else>
                {{ $date(getLatestDate(active_schedule.last_progress_updated_at, getLatestDate(active_schedule.updated_at, active_schedule.created_at)), 'DD MMMM YYYY, hh:mma') }}
              </template>
            </span>
          </div>
          <div v-if="is_schedule_editable && active_tab === 'gantt-chart' && schedule_dirtiness" class="flex items-center gap-2 text-xs font-medium">
            •
            <IconHawkAlertTriangle class="!w-4 !h-4 text-warning-600" />
            <span class="text-warning-700">
              {{ $t('Unpublished schedule') }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="flex items-center">
      <template v-if="is_schedule_editable && !flags.hide_save_and_submit && active_tab === 'gantt-chart'">
        <HawkBadge v-if="schedule_dirtiness && active_schedule.actions.modify_unpublished_activities" size="lg" color="blue" :custom_color_background_opacity="1" custom_classes="!h-9 !pl-3.5 !pr-1.5" class="mr-2">
          <IconHawkSaveOne class="!w-4 !h-4 text-primary-600" />
          <div class="text-sm font-medium text-primary-700 mx-1">
            {{ $t('You have unsaved changes') }}
          </div>
          <HawkButton
            size="xxs"
            class="!p-3"
            :rounded="true"
            :loading="state.is_saving"
            @click="onSave"
          >
            {{ $t('Save') }}
          </HawkButton>
        </HawkBadge>
        <HawkBadge v-else-if="active_schedule.actions.publish_unpublish" size="lg" custom_color="#FFFAEB" :custom_color_background_opacity="1" custom_classes="!h-9 !pl-3.5 !pr-1.5" class="mr-2">
          <IconHawkAlertTriangle class="!w-4 !h-4 text-warning-600" />
          <div class="text-sm font-medium text-warning-700 mx-1">
            {{ $t('Schedule is not published') }}
          </div>
          <HawkButton
            size="xxs"
            class="!p-3"
            color="warning"
            :rounded="true"
            :loading="state.is_saving"
            @click="onSubmit('publish')"
          >
            {{ $t('Publish') }}
          </HawkButton>
        </HawkBadge>
      </template>
      <HawkBadge v-if="!is_schedule_editable && active_tab === 'gantt-chart' && view_dirtiness" size="lg" color="blue" :custom_color_background_opacity="1" custom_classes="!h-9 !pl-3.5 !pr-1.5" class="mr-2">
        <IconHawkSaveOne class="!w-4 !h-4 text-primary-600" />
        <div class="text-sm font-medium text-primary-700 mx-1">
          <template v-if="active_view?.uid === '__default'">
            {{ $t('Your view has been modified') }}
          </template>
          <template v-else>
            {{ $t('This view has unsaved changes') }}
          </template>
        </div>
        <HawkButton
          size="xxs"
          class="!p-3"
          :rounded="true"
          :loading="state.is_saving"
          @click="updateView"
        >
          <template v-if="active_view?.uid === '__default'">
            {{ $t('Create view') }}
          </template>
          <template v-else>
            {{ $t('Save view') }}
          </template>
        </HawkButton>
      </HawkBadge>
      <div class="cursor-pointer flex items-center" @click="openShareModal">
        <template v-if="Object.values(active_schedule.members).length">
          <HawkMembers
            :members="users_and_teams.users.concat(users_and_teams.teams)"
            :max_badges_to_display="5"
            :has_avatar="true"
            popover_position="right"
            size="xs"
            type="group"
          />
        </template>
        <HawkButton v-else type="outlined">
          <IconHawkShareSeven />
          {{ $t("Share") }}
        </HawkButton>
      </div>
      <HawkMenu
        :items="menu_items"
        class="ml-2"
        additional_trigger_classes="ring-gray-100"
        additional_dropdown_classes="!mt-1"
        additional_item_classes="w-[208px] h-10 hover:bg-gray-50 cursor-pointer rounded-lg !px-2 mb-1"
      >
        <template #trigger>
          <HawkButton icon type="outlined">
            <IconHawkDotsVertical class="w-5 h-5" />
          </HawkButton>
        </template>
        <template #item="{ item }">
          <div v-if="!['sync_progress', 'export'].includes(item.name)" class="flex items-center text-sm font-medium text-gray-700">
            <component :is="item.left_icon" class="inline mr-2 text-gray-500" />
            {{ item.label }}
          </div>
          <HawkMenu
            v-else
            :items="item.submenu_items"
            class="flex items-center w-full"
            additional_trigger_classes="w-full !ring-0"
            additional_dropdown_classes="right-[108%] !-top-[11px] !bottom-auto"
            additional_item_classes="w-[232px] h-10 hover:bg-gray-50 cursor-pointer rounded-lg !px-2 mb-1"
            position="bottom-left"
          >
            <template #trigger>
              <div class="flex justify-between w-full py-3">
                <div class="flex items-center text-sm font-medium text-gray-700">
                  <component :is="item.left_icon" class="inline mr-2 text-gray-500" />
                  {{ item.label }}
                </div>
                <IconHawkChevronRight class="text-gray-600" />
              </div>
            </template>
            <template #item="{ item: sync_item }">
              <div class="flex items-center text-sm font-medium text-gray-700" :class="{ 'pointer-events-none': item.disabled }">
                <component :is="sync_item.left_icon" class="inline mr-2 text-gray-500" />
                {{ sync_item.label }}
              </div>
            </template>
          </HawkMenu>
        </template>
      </HawkMenu>
    </div>
  </div>
</template>
